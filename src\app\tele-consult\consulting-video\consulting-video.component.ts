import { Component, Input, OnInit, OnDestroy, ViewChild, Output, EventEmitter, ElementRef } from '@angular/core';
import { throwError as observableThrowError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {
  OpenviduSessionComponent,
  StreamEvent,
  Session,
  UserModel,
  OpenViduLayout,
  OvSettings,
  OpenViduLayoutOptions,
  SessionDisconnectedEvent,
  Publisher,
  OpenVidu,
  VideoElementEvent
} from 'openvidu-angular';
import { TeleConsultService } from '../tele-consult.service';
import { ActivatedRoute } from '@angular/router';
import * as Settings from '../../config/settings';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-consulting-video',
  templateUrl: './consulting-video.component.html',
  styleUrls: ['./consulting-video.component.css']
})
export class ConsultingVideoComponent implements OnInit, OnDestroy {
  OPENVIDU_SERVER_URL = Settings.OPENVIDU_SERVER_URL;
  OPENVIDU_SERVER_SECRET = Settings.OPENVIDU_SECRET;
  mySessionId: string;
  myUserName = 'Participant' + Math.floor(Math.random() * 100);
  tokens: string[] = [];
  deviceTokens: string[] = [];
  session = false;
  ovSession: Session;
  ovLocalUsers: UserModel[];
  ovLayout: OpenViduLayout;
  ovLayoutOptions: OpenViduLayoutOptions;
  ovSettings: OvSettings;

  @Input() consultationId: any;
  @Input() participantName: any;
  @Input() closeVideoSession: any;
  @Output() showJoinButton: EventEmitter<boolean> = new EventEmitter();
  @ViewChild('ovSessionComponent') public ovSessionComponent: OpenviduSessionComponent;

  // New for multiple cameras
  @ViewChild('secondaryContainer') secondaryContainer: ElementRef;

  videoToken = '';
  appointmentId = '';
  sessionJoined = false;
  videoDevices: any[] = [];
  audioDevices: any[] = [];

  // For second camera
  secondaryOV: OpenVidu;
  secondarySession: Session;
  secondaryPublisher: Publisher;
  secondaryToken: string;
  isSecondaryCameraAvailable = false;

  // Device monitoring
  private deviceMonitorInterval: any;
  private currentSecondaryDeviceId: string | null = null;
  private lastKnownVideoDeviceIds: string[] = [];
  private isProcessingDeviceChange = false;
  private lastDeviceCheckTime = 0;
  private deviceChangeBuffer: any[] = [];
  private isUserInteracting = false;
  private deviceMonitoringEnabled = false;
  private sessionInitialized = false;

  constructor(
    private httpClient: HttpClient,
    private route: ActivatedRoute,
    private teleConsultService: TeleConsultService,
    private notificationService: ToastrService
  ) {
    this.mySessionId = this.consultationId;
  }

  ngOnInit() {
    
    var OV = new OpenVidu();
    OV.getDevices().then(devices => {
      this.videoDevices = devices.filter(device => device.kind === 'videoinput');
      this.audioDevices = devices.filter(device => device.kind === 'audioinput');
      console.log(this.videoDevices.length, this.audioDevices.length);

      if (this.videoDevices.length > 0 && this.audioDevices.length > 0) {
        this.ovSettings = {
          chat: false,
          autopublish: true,
          toolbarButtons: {
            audio: true,
            video: true,
            screenShare: false,
            fullscreen: true,
            layoutSpeaking: false,
            exit: false,
          }
        };
      } else if (this.audioDevices.length > 0) {
        this.ovSettings = {
          chat: false,
          autopublish: true,
          toolbarButtons: {
            audio: true,
            video: false,
            screenShare: false,
            fullscreen: true,
            layoutSpeaking: false,
            exit: false,
          }
        };
      } else {
        this.notificationService.error('No video or audio devices have been found. Please, connect at least one. Please refresh.');
      }

      // Join both sessions
      this.joinSession();
      this.joinDeviceSession();

      // Initialize secondary camera (USB camera) if available
      if (this.videoDevices.length > 1) {
        this.initSecondaryCamera(); // Use the new method for better USB camera detection
      }

      // Initialize device tracking but delay monitoring start
      this.lastKnownVideoDeviceIds = this.videoDevices.map(device => device.deviceId);

      // Delay device monitoring start to allow session to fully initialize
      setTimeout(() => {
        this.sessionInitialized = true;
        this.deviceMonitoringEnabled = true;
        this.startDeviceMonitoring();
      }, 15000); // Wait 15 seconds before starting monitoring

      this.myUserName = this.participantName;
      console.log('Participant:' + this.participantName);
    });
  }

  ngOnDestroy() {
    // Stop device monitoring
    this.stopDeviceMonitoring();

    // Clean up secondary camera session
    if (this.secondaryPublisher && this.secondarySession) {
      this.secondarySession.unpublish(this.secondaryPublisher);
    }

    if (this.secondarySession) {
      this.secondarySession.disconnect();
    }

  }

  /**
   * Find and initialize the secondary camera (USB camera)
   * @param deviceId Optional specific device ID to use
   */
  initSecondaryCamera(deviceId?: string) {
    this.isSecondaryCameraAvailable = true;

    // If specific deviceId provided, use it directly
    if (deviceId) {
      this.joinSecondaryCameraWithDevice(deviceId);
      return;
    }

    // Auto-detect USB camera
    const usbCamera = this.findUSBCamera();
    if (usbCamera) {
      console.log('USB camera detected:', usbCamera.label);
      this.joinSecondaryCameraWithDevice(usbCamera.deviceId);
    } else {
      console.warn('No USB camera detected. Using fallback camera selection.');
      this.joinSecondaryCameraIfAvailable();
    }
  }

  /**
   * Find USB camera by analyzing device labels
   */
  private findUSBCamera() {
    console.log('Available video devices:', this.videoDevices.map(d => ({ deviceId: d.deviceId, label: d.label })));

    // First, try to find devices with USB-specific patterns
    let usbCamera = this.videoDevices.find(device => {
      const label = device.label.toLowerCase();
      return (
        label.includes('usb') ||
        label.includes('external') ||
        label.includes('webcam') ||
        label.includes('logitech') ||
        label.includes('microsoft') ||
        label.includes('creative') ||
        label.includes('genius')
      );
    });

    // If no USB-specific camera found, exclude built-in cameras
    if (!usbCamera) {
      usbCamera = this.videoDevices.find(device => {
        const label = device.label.toLowerCase();
        return (
          !label.includes('facetime') &&
          !label.includes('integrated') &&
          !label.includes('built-in') &&
          !label.includes('internal')
        );
      });
    }

    // Fallback to second camera if available
    if (!usbCamera && this.videoDevices.length > 1) {
      usbCamera = this.videoDevices[1];
    }

    console.log('Selected USB camera:', usbCamera ? { deviceId: usbCamera.deviceId, label: usbCamera.label } : 'None');
    return usbCamera;
  }

  /**
   * Get list of available cameras for debugging
   */
  getAvailableCameras() {
    return this.videoDevices.map(device => ({
      deviceId: device.deviceId,
      label: device.label,
      kind: device.kind
    }));
  }

  /**
   * Call this method when mute/unmute buttons are clicked to prevent device monitoring interference
   */
  onAudioVideoToggle() {
    console.log('Audio/Video toggle detected, pausing device monitoring');
    // Clear any pending device changes
    this.deviceChangeBuffer = [];
    this.lastDeviceCheckTime = Date.now();
    // Pause for longer duration to prevent interference
    this.pauseDeviceMonitoring(5000); // Pause for 5 seconds
  }

  /**
   * Public method to manually switch to secondary camera
   */
  switchToSecondaryCamera(deviceId?: string) {
    if (deviceId) {
      this.initSecondaryCamera(deviceId);
    } else {
      this.initSecondaryCamera();
    }
  }

  /**
   * Public method to disconnect secondary camera
   */
  disconnectSecondaryCamera() {
    if (this.isSecondaryCameraAvailable) {
      this.handleSecondaryCameraDisconnect();
    }
  }

  /**
   * Start monitoring for device changes (camera connect/disconnect)
   */
  private startDeviceMonitoring() {
    // Only start if monitoring is enabled
    if (!this.deviceMonitoringEnabled) {
      console.log('Device monitoring disabled, skipping start');
      return;
    }

    // Monitor device changes every 15 seconds (very infrequent to avoid interference)
    this.deviceMonitorInterval = setInterval(() => {
      // Only check if monitoring is enabled and user is not interacting
      if (this.deviceMonitoringEnabled && !this.isUserInteracting && !this.isProcessingDeviceChange && this.sessionInitialized) {
        this.checkDeviceChanges();
      }
    }, 15000);

    console.log('Device monitoring started with 15-second intervals');
  }

  /**
   * Stop device monitoring
   */
  private stopDeviceMonitoring() {
    if (this.deviceMonitorInterval) {
      clearInterval(this.deviceMonitorInterval);
      this.deviceMonitorInterval = null;
      console.log('Device monitoring stopped');
    }
  }

  /**
   * Temporarily pause device monitoring during user interactions
   */
  private pauseDeviceMonitoring(durationMs: number = 3000) {
    this.isProcessingDeviceChange = true;
    this.isUserInteracting = true;

    console.log(`Device monitoring paused for ${durationMs}ms`);

    setTimeout(() => {
      this.isProcessingDeviceChange = false;
      this.isUserInteracting = false;
      console.log('Device monitoring resumed');
    }, durationMs);
  }

  /**
   * Check for device changes and handle camera disconnect/connect
   * Uses buffering approach to prevent false triggers from temporary device state changes
   */
  private async checkDeviceChanges() {
    // Prevent multiple simultaneous device change processing
    if (this.isProcessingDeviceChange || this.isUserInteracting || !this.deviceMonitoringEnabled || !this.sessionInitialized) {
      return;
    }

    const currentTime = Date.now();

    // Don't check too frequently
    if (currentTime - this.lastDeviceCheckTime < 8000) {
      return;
    }

    this.lastDeviceCheckTime = currentTime;
    this.isProcessingDeviceChange = true;

    try {
      const OV = new OpenVidu();
      const currentDevices = await OV.getDevices();
      const currentVideoDevices = currentDevices.filter(device => device.kind === 'videoinput');
      const currentVideoDeviceIds = currentVideoDevices.map(device => device.deviceId).sort();
      const lastKnownIds = [...this.lastKnownVideoDeviceIds].sort();

      // Check if secondary camera was removed (this is critical and should be immediate)
      if (this.isSecondaryCameraAvailable && this.currentSecondaryDeviceId) {
        const secondaryCameraExists = currentVideoDeviceIds.includes(this.currentSecondaryDeviceId);

        if (!secondaryCameraExists) {
          console.log('Secondary camera disconnected, switching to single camera mode');
          await this.handleSecondaryCameraDisconnect();
          this.lastKnownVideoDeviceIds = currentVideoDeviceIds;
          this.videoDevices = currentVideoDevices;
          this.isProcessingDeviceChange = false;
          return;
        }
      }

      // Use buffering approach for device additions to prevent false triggers
      const devicesAdded = currentVideoDeviceIds.filter(id => !lastKnownIds.includes(id));
      const devicesRemoved = lastKnownIds.filter(id => !currentVideoDeviceIds.includes(id));

      // Only process significant changes
      const significantChange = devicesAdded.length > 0 || devicesRemoved.length > 0;

      if (significantChange) {
        // Add to buffer for verification
        this.deviceChangeBuffer.push({
          timestamp: currentTime,
          added: devicesAdded,
          removed: devicesRemoved,
          deviceIds: currentVideoDeviceIds
        });

        // Keep only recent entries (last 30 seconds)
        this.deviceChangeBuffer = this.deviceChangeBuffer.filter(entry =>
          currentTime - entry.timestamp < 30000
        );

        // Only act if we have consistent changes over multiple checks
        if (this.deviceChangeBuffer.length >= 2) {
          const recentChanges = this.deviceChangeBuffer.slice(-2);
          const consistentAdditions = recentChanges.every(change =>
            change.added.length > 0 && change.added.some(id => devicesAdded.includes(id))
          );

          if (consistentAdditions && !this.isSecondaryCameraAvailable && devicesAdded.length > 0) {
            console.log('Consistent new camera devices detected:', devicesAdded);
            this.videoDevices = currentVideoDevices;
            this.lastKnownVideoDeviceIds = currentVideoDeviceIds;

            // Clear buffer after processing
            this.deviceChangeBuffer = [];

            // Connect new camera after a delay
            setTimeout(() => {
              if (!this.isSecondaryCameraAvailable && !this.isUserInteracting) {
                this.handleNewCameraConnect();
              }
            }, 2000);
          }
        }
      } else {
        // No changes, just update the device list
        this.videoDevices = currentVideoDevices;
        this.lastKnownVideoDeviceIds = currentVideoDeviceIds;
      }

    } catch (error) {
      console.error('Error checking device changes:', error);
    } finally {
      this.isProcessingDeviceChange = false;
    }
  }

  /**
   * Handle secondary camera disconnect - switch to single camera mode
   */
  private async handleSecondaryCameraDisconnect() {
    try {
      // Stop secondary camera session
      if (this.secondaryPublisher) {
        await this.secondarySession.unpublish(this.secondaryPublisher);
        this.secondaryPublisher = null;
      }

      if (this.secondarySession) {
        await this.secondarySession.disconnect();
        this.secondarySession = null;
      }

      // Reset secondary camera state
      this.isSecondaryCameraAvailable = false;
      this.currentSecondaryDeviceId = null;

      // Clear secondary container
      if (this.secondaryContainer?.nativeElement) {
        this.secondaryContainer.nativeElement.innerHTML = '';
      }

      console.log('Successfully switched to single camera mode');
      this.notificationService.info('Secondary camera disconnected. Switched to single camera mode.');

    } catch (error) {
      console.error('Error handling secondary camera disconnect:', error);
    }
  }

  /**
   * Handle new camera connection - check if it should be used as secondary
   */
  private handleNewCameraConnect() {
    // Only auto-connect if we don't already have a secondary camera
    if (!this.isSecondaryCameraAvailable && this.videoDevices.length > 1) {
      console.log('Attempting to initialize new camera as secondary camera');
      this.initSecondaryCamera();
      this.notificationService.info('New camera detected and connected as secondary camera.');
    }
  }

  /**
   * Initialize secondary camera with specific device ID
   */
  private async joinSecondaryCameraWithDevice(deviceId: string) {
    try {
      const response = await this.teleConsultService.getDeviceVideoToken(this.consultationId).toPromise();
      this.secondaryToken = response['token'];

      this.secondaryOV = new OpenVidu();
      this.secondarySession = this.secondaryOV.initSession();

      this.secondarySession.on('streamCreated', (event: StreamEvent) => {
        const subscriber = this.secondarySession.subscribe(event.stream, undefined);
        subscriber.on('videoElementCreated', (ev: VideoElementEvent) => {
          this.secondaryContainer?.nativeElement?.appendChild(ev.element);
        });
      });

      await this.secondarySession.connect(this.secondaryToken, {
        clientData: this.participantName + '_secondary',
      });

      this.secondaryPublisher = this.secondaryOV.initPublisher(undefined, {
        videoSource: deviceId,
        publishAudio: false,
        publishVideo: true,
        insertMode: 'APPEND',
      });

      this.secondarySession.publish(this.secondaryPublisher);

      // Track the current secondary device ID for monitoring
      this.currentSecondaryDeviceId = deviceId;

      console.log('Secondary camera stream published with device:', deviceId);
    } catch (error) {
      console.error('Error streaming secondary camera:', error);
      this.isSecondaryCameraAvailable = false;
      this.currentSecondaryDeviceId = null;
    }
  }

  async joinSecondaryCameraIfAvailable() {
    if (this.videoDevices.length < 2) {
      console.warn('Only one camera found. Skipping secondary camera stream.');
      return;
    }

    // Use the USB camera detection logic
    const usbCamera = this.findUSBCamera();
    const deviceId = usbCamera ? usbCamera.deviceId : this.videoDevices[1].deviceId;

    await this.joinSecondaryCameraWithDevice(deviceId);
  }

  async joinSession() {
    console.log('Consulting video cons id::' + this.consultationId);
    this.teleConsultService.getVideoToken(this.consultationId).subscribe(
      (data) => {
        const msg = data['message'];
        if (msg && msg != undefined) {
          this.notificationService.error(msg);
        } else {
          this.videoToken = data['token'];
          this.sessionJoined = true;
          this.tokens.push(this.videoToken);
          this.session = true;
        }
      },
      (err) => {
        console.log('ERROR:' + err);
        this.notificationService.error('Please refresh page');
      }
    );
  }

  joinDeviceSession() {
    console.log('Consulting device video cons id::' + this.consultationId);
    this.teleConsultService.getDeviceVideoToken(this.consultationId).subscribe(
      (data) => {
        this.videoToken = data['token'];
        this.sessionJoined = true;
        this.deviceTokens.push(this.videoToken);
        this.session = true;
      },
      (err) => {
        console.log('ERROR:' + err);
      }
    );
  }

  handlerSessionCreatedEvent(session: Session): void {
    console.log('SESSION CREATED EVENT', session);
    session.on('streamCreated', (event: StreamEvent) => {
      console.log('SESSION STREAM CREATED');
      // Pause device monitoring briefly when streams are created to avoid conflicts
      this.pauseDeviceMonitoring(2000);
    });
    session.on('streamDestroyed', (event: StreamEvent) => {
      console.log('SESSION STREAM DESTROYED');
      // Pause device monitoring briefly when streams are destroyed to avoid conflicts
      this.pauseDeviceMonitoring(2000);
    });
    session.on('sessionDisconnected', (event: SessionDisconnectedEvent) => {
      this.session = false;
      this.tokens = [];
      this.showJoinButton.emit(false);
    });

    this.myMethod();
  }

  handlerPublisherCreatedEvent(publisher: Publisher) {
    publisher.on('streamCreated', (e) => {
      console.log('Publisher streamCreated', e);
      // Pause device monitoring when publisher stream is created
      this.pauseDeviceMonitoring(3000);
    });

    // Add event listeners for audio/video toggle to prevent device monitoring interference
    publisher.on('accessAllowed', () => {
      console.log('Publisher access allowed');
      this.pauseDeviceMonitoring(3000);
    });

    publisher.on('accessDenied', () => {
      console.log('Publisher access denied');
      this.pauseDeviceMonitoring(3000);
    });

    // Listen for audio/video property changes
    publisher.on('streamPropertyChanged', (event) => {
      console.log('Publisher stream property changed:', event);
      // This is likely a mute/unmute event
      this.onAudioVideoToggle();
    });
  }

  handlerErrorEvent(event): void {
    console.log(event);
  }

  myMethod() {
    this.ovLocalUsers = this.ovSessionComponent.getLocalUsers();
    this.ovLayout = this.ovSessionComponent.getOpenviduLayout();
    this.ovLayoutOptions = this.ovSessionComponent.getOpenviduLayoutOptions();
  }

  getToken(): Promise<string> {
    return this.createSession(this.consultationId).then((sessionId) => {
      return this.createToken(sessionId);
    });
  }

  createSession(sessionId) {
    return new Promise((resolve, reject) => {
      const body = JSON.stringify({ customSessionId: sessionId });
      const options = {
        headers: new HttpHeaders({
          Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET),
          'Content-Type': 'application/json',
        }),
      };
      return this.httpClient
        .post(this.OPENVIDU_SERVER_URL + '/api/sessions', body, options)
        .pipe(
          catchError((error) => {
            if (error.status === 409) {
              resolve(sessionId);
            } else {
              console.warn('No connection to OpenVidu Server:', this.OPENVIDU_SERVER_URL);
              if (window.confirm('No connection to OpenVidu Server. Accept certificate?')) {
                // location.assign(this.OPENVIDU_SERVER_URL + '/accept-certificate');
              }
            }
            return observableThrowError(error);
          })
        )
        .subscribe((response) => {
          resolve(response['id']);
        });
    });
  }

  createToken(sessionId): Promise<string> {
    return new Promise((resolve, reject) => {
      const body = JSON.stringify({ session: sessionId });
      const options = {
        headers: new HttpHeaders({
          Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET),
          'Content-Type': 'application/json',
        }),
      };
      return this.httpClient
        .post(this.OPENVIDU_SERVER_URL + '/api/tokens', body, options)
        .pipe(
          catchError((error) => {
            reject(error);
            return observableThrowError(error);
          })
        )
        .subscribe((response) => {
          resolve(response['token']);
        });
    });
  }
}
