<!-- Main OpenVidu session -->
<opv-session
  #ovSessionComponent
  [sessionName]="mySessionId"
  [user]="myUserName"
  [tokens]="tokens"
  [ovSettings]="ovSettings"
  (sessionCreated)="handlerSessionCreatedEvent($event)"
  (participantCreated)="handlerPublisherCreatedEvent($event)"
  (error)="handlerErrorEvent($event)"
  *ngIf="session && sessionJoined">
</opv-session>

<!-- Additional container for secondary camera (e.g., otoscope) -->
<div
  *ngIf="videoDevices.length > 1 && sessionJoined"
  #secondaryContainer
  id="secondary-camera-container"
  class="secondary-video">
</div>
