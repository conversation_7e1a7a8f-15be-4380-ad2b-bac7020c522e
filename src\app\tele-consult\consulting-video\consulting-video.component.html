<!-- Main OpenVidu session -->
<opv-session
  #ovSessionComponent
  [sessionName]="mySessionId"
  [user]="myUserName"
  [tokens]="tokens"
  [ovSettings]="ovSettings"
  (sessionCreated)="handlerSessionCreatedEvent($event)"
  (participantCreated)="handlerPublisherCreatedEvent($event)"
  (error)="handlerErrorEvent($event)"
  *ngIf="session && sessionJoined">
</opv-session>

<!-- Additional container for secondary camera (e.g., USB camera) -->
<!-- <div
  *ngIf="isSecondaryCameraAvailable && sessionJoined"
  #secondaryContainer
  id="secondary-camera-container"
  class="secondary-video">
</div> -->
